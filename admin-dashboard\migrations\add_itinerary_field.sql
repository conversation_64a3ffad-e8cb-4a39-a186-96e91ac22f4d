-- Migration: Add itinerary field to tour_packages table
-- This migration adds an optional itinerary field to store rich text content for tour packages

-- Check if the itinerary column already exists before adding it
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'tour_packages'
    AND COLUMN_NAME = 'itinerary'
);

-- Add the itinerary column if it doesn't exist
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE tour_packages ADD COLUMN itinerary TEXT AFTER description',
    'SELECT "Column itinerary already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Verify the column was added
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'tour_packages'
AND COLUMN_NAME = 'itinerary';
