<?php
// Include database configuration and models
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/classes/models.php';
require_once 'admin-dashboard/classes/additional_models.php';

// Function to determine the back navigation for packages
function getPackageBackNavigation() {
    $referer = $_SERVER['HTTP_REFERER'] ?? '';
    $currentHost = $_SERVER['HTTP_HOST'] ?? '';

    // Check for explicit back parameter in URL (for header dropdown navigation)
    if (isset($_GET['from'])) {
        switch ($_GET['from']) {
            case 'tours':
                return [
                    'url' => 'tours.php#packages',
                    'text' => 'Back to Tours & Packages'
                ];
            case 'index':
                return [
                    'url' => 'index.php#packages',
                    'text' => 'Back to Featured Packages'
                ];
        }
    }

    // Check if referer is from the same domain
    if (!empty($referer) && strpos($referer, $currentHost) !== false) {
        $refererPath = parse_url($referer, PHP_URL_PATH);
        $refererFile = basename($refererPath);
        $refererQuery = parse_url($referer, PHP_URL_QUERY);

        // Determine back link based on referring page
        switch ($refererFile) {
            case 'tours.php':
                $anchor = '';
                // Check if there were filters applied
                if (!empty($refererQuery)) {
                    $anchor = '?' . $refererQuery . '#packages';
                } else {
                    $anchor = '#packages';
                }
                return [
                    'url' => 'tours.php' . $anchor,
                    'text' => 'Back to Tours & Packages'
                ];
            case 'index.php':
                return [
                    'url' => 'index.php#packages',
                    'text' => 'Back to Featured Packages'
                ];
            case 'destination-details.php':
                // If coming from destination details, go back to that destination
                return [
                    'url' => $referer,
                    'text' => 'Back to Destination'
                ];
            default:
                // If coming from any other page on the same domain, go back to that page
                if (strpos($referer, 'package-details.php') === false) {
                    return [
                        'url' => $referer,
                        'text' => 'Back to Previous Page'
                    ];
                }
        }
    }

    // Default fallback - prioritize tours page over index
    return [
        'url' => 'tours.php#packages',
        'text' => 'Back to Tours & Packages'
    ];
}

// Get back navigation details
$backNav = getPackageBackNavigation();

// Get package ID from URL
$packageId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($packageId <= 0) {
    header('Location: index.php');
    exit;
}

// Initialize models
$tourPackageModel = new TourPackage();
$imageModel = new Image();
$destinationModel = new Destination();

// Fetch package details with tour type information
$package = $tourPackageModel->findByIdWithDetails($packageId);

if (!$package) {
    header('Location: index.php');
    exit;
}

// Fetch destination information if package has a destination
$destination = null;
if ($package['destination_id']) {
    $destination = $destinationModel->findById($package['destination_id']);
}

// Fetch all images for this package
$images = $imageModel->findByTourPackage($packageId);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo Utils::displayText($package['name']); ?> | Meleva Tours & Travel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <style>
        body {
            padding-top: 50px;
        }

        .gradient-text {
            background: linear-gradient(135deg, #f97316, #ea580c);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* Swiper Container */
        .swiper-container {
            position: relative;
            width: 100%;
            height: 300px;
            border-radius: 1rem;
            overflow: hidden;
            background: #ccc;
        }
        
        /* Larger height for desktop */
        @media (min-width: 768px) {
            .swiper-container {
                height: 500px;
            }
        }

        /* Slide Image */
        .swiper-slide img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        /* Navigation Buttons */
        .swiper-button-prev,
        .swiper-button-next {
            background: rgba(0, 0, 0, 0.5);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            transition: all 0.3s ease;
            display: none; /* Hidden by default */
        }
        
        .swiper-button-prev:hover,
        .swiper-button-next:hover {
            background: rgba(0, 0, 0, 0.8);
        }
        
        .swiper-button-prev::after,
        .swiper-button-next::after {
            color: white;
            font-size: 20px;
        }
        
        /* Show navigation on larger screens */
        @media (min-width: 768px) {
            .swiper-button-prev,
            .swiper-button-next {
                display: flex;
            }
        }
        
        /* Pagination Styles */
        .swiper-pagination {
            position: absolute;
            bottom: 20px !important;
            z-index: 10;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .swiper-pagination-bullet {
            width: 10px;
            height: 10px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 1;
            margin: 0 5px !important;
            transition: all 0.3s ease;
        }
        
        .swiper-pagination-bullet-active {
            width: 20px;
            background: #f97316;
            border-radius: 5px;
        }
        
        /* No Images Placeholder */
        .no-images-placeholder {
            width: 100%;
            height: 300px;
            background: #f3f4f6;
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: #6b7280;
        }

        /* Prose styling for itinerary content */
        .prose {
            color: #374151;
            line-height: 1.75;
            max-width: none;
        }

        /* Headings */
        .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
            color: #111827;
            font-weight: 600;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        .prose h1 { font-size: 1.875rem; }
        .prose h2 { font-size: 1.5rem; }
        .prose h3 { font-size: 1.25rem; }
        .prose h4 { font-size: 1.125rem; }

        /* Paragraphs */
        .prose p {
            margin-bottom: 1em;
        }

        /* Lists */
        .prose ul, .prose ol {
            margin: 1em 0;
            padding-left: 1.5em;
        }
        .prose li {
            margin-bottom: 0.5em;
        }
        .prose ul li {
            list-style-type: disc;
            display: list-item;
        }
        .prose ol li {
            list-style-type: decimal;
            display: list-item;
        }

        /* Text formatting */
        .prose strong, .prose b {
            font-weight: 700;
            color: #111827;
        }
        .prose em, .prose i {
            font-style: italic;
        }

        /* Links */
        .prose a {
            color: #f97316;
            text-decoration: underline;
        }
        .prose a:hover {
            color: #ea580c;
        }

        /* TinyMCE specific elements */
        .prose .mce-content-body {
            line-height: 1.75;
        }

        /* Ensure lists display properly */
        .prose ul {
            list-style-position: outside;
            list-style-type: disc;
        }
        .prose ol {
            list-style-position: outside;
            list-style-type: decimal;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Include Header Navigation -->
    <?php include 'header.php'; ?>

    <!-- Package Details -->
    <section class="py-20 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Back Button -->
            <div class="mb-8">
                <a href="<?php echo htmlspecialchars($backNav['url']); ?>" class="inline-flex items-center text-orange-500 hover:text-orange-600 font-medium">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                    </svg>
                    <?php echo htmlspecialchars($backNav['text']); ?>
                </a>
            </div>

            <!-- Package Header -->
            <div class="text-center mb-16">
                <h1 class="text-3xl md:text-4xl font-semibold text-gray-900 mb-6">
                    <?php
                    $packageName = $package['name'];
                    $tourType = $package['type_name'] ?: 'Tour';
                    $duration = $package['duration'];

                    $lowerTourType = strtolower($tourType);
                    $typeImpliesDuration = strpos($lowerTourType, 'day') !== false ||
                                          strpos($lowerTourType, 'overnight') !== false ||
                                          strpos($lowerTourType, 'night') !== false;

                    if ($typeImpliesDuration) {
                        $formattedName = $tourType . ' ' . $packageName;
                    } elseif (!empty($duration)) {
                        $formattedName = $duration . ' ' . $packageName . ' ' . $tourType;
                    } else {
                        $formattedName = $packageName . ' ' . $tourType;
                    }

                    echo Utils::displayText($formattedName);
                    ?>
                </h1>
                <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mb-8"></div>
                <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
                    <?php echo Utils::displayText($package['description'] ?: 'Discover this amazing tour package with unique experiences.'); ?>
                </p>
            </div>

            <!-- Main Content -->
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-12 mb-16">
                <!-- Images Section with Swiper (3/4 width) -->
                <div class="lg:col-span-3">
                    <?php if (!empty($images)): ?>
                        <div class="swiper-container">
                            <div class="swiper-wrapper">
                                <?php foreach ($images as $index => $image): ?>
                                    <div class="swiper-slide">
                                        <img src="admin-dashboard/<?php echo htmlspecialchars($image['url']); ?>"
                                             alt="<?php echo Utils::displayText($image['alt_text'] ?: $package['name']); ?>"
                                             loading="lazy">
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <!-- Navigation buttons -->
                            <div class="swiper-button-prev"></div>
                            <div class="swiper-button-next"></div>
                            <!-- Pagination -->
                            <div class="swiper-pagination"></div>
                        </div>
                    <?php else: ?>
                        <div class="no-images-placeholder">
                            <svg class="w-16 h-16 mx-auto mb-2" fill="none" stroke="currentColor" stroke-width="1" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <p>No Images Available</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Details Section (1/4 width) -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-2xl shadow-lg p-6 mb-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Package Details</h2>

                        <div class="space-y-4 mb-8">
                            <!-- Price -->
                            <div class="flex justify-between items-center py-3 border-b border-gray-200">
                                <span class="font-medium text-gray-600">Starting Price:</span>
                                <span class="text-xl font-bold gradient-text">
                                    $<?php echo number_format($package['price']); ?>
                                </span>
                            </div>

                            <!-- Duration -->
                            <?php if (!empty($package['duration'])): ?>
                            <div class="flex justify-between items-center py-3 border-b border-gray-200">
                                <span class="font-medium text-gray-600">Duration:</span>
                                <span class="text-md font-semibold text-gray-900">
                                    <?php echo Utils::displayText($package['duration']); ?>
                                </span>
                            </div>
                            <?php endif; ?>

                            <!-- Package Type -->
                            <div class="flex justify-between items-center py-3 border-b border-gray-200">
                                <span class="font-medium text-gray-600">Tour Type:</span>
                                <span class="text-md font-semibold text-gray-900">
                                    <?php echo Utils::displayText($package['type_name'] ?: 'Tour'); ?>
                                </span>
                            </div>

                            <!-- Destination -->
                            <?php if ($destination): ?>
                            <div class="flex justify-between items-center py-3 border-b border-gray-200">
                                <span class="font-medium text-gray-600">Destination:</span>
                                <span class="text-md font-semibold text-gray-900">
                                    <?php echo htmlspecialchars($destination['name']); ?>
                                </span>
                            </div>

                            <!-- Location -->
                            <?php if (!empty($destination['location'])): ?>
                            <div class="flex justify-between items-center py-3 border-b border-gray-200">
                                <span class="font-medium text-gray-600">Location:</span>
                                <span class="text-md font-semibold text-gray-900">
                                    <?php echo htmlspecialchars($destination['location']); ?>
                                </span>
                            </div>
                            <?php endif; ?>
                            <?php endif; ?>
                        </div>

                        <!-- Quote CTA -->
                        <div class="text-center space-y-3">
                            <a href="request-quote.php?package_id=<?php echo $packageId; ?>"
                               class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-full font-semibold transition duration-300 transform hover:scale-105 inline-block w-full">
                                Get Custom Quote
                            </a>
                            <a href="contact.php"
                               class="border-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white px-6 py-2 rounded-full font-medium transition duration-300 inline-block w-full text-sm">
                                Ask Questions
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Itinerary Section -->
            <?php if (!empty($package['itinerary'])): ?>
            <div class="mt-16">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-semibold text-gray-900 mb-4">Tour Itinerary</h2>
                    <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto"></div>
                </div>

                <div class="bg-white rounded-2xl shadow-lg p-8 max-w-4xl mx-auto">
                    <div class="prose prose-lg max-w-none">
                        <?php
                        $itinerary = $package['itinerary'];
                        // Decode HTML entities multiple times to handle nested encoding
                        $itinerary = html_entity_decode($itinerary, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                        $itinerary = html_entity_decode($itinerary, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                        // Don't use nl2br since TinyMCE already provides proper HTML formatting
                        // Just output the HTML directly (it's safe since it comes from admin input)
                        echo $itinerary;
                        ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

        </div>
    </section>

    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            <?php if (!empty($images)): ?>
            const swiper = new Swiper('.swiper-container', {
                loop: true,
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                    dynamicBullets: false,
                    renderBullet: function (index, className) {
                        return '<span class="' + className + '"></span>';
                    },
                },
                keyboard: {
                    enabled: true,
                },
                breakpoints: {
                    320: {
                        navigation: {
                            enabled: false
                        }
                    },
                    768: {
                        navigation: {
                            enabled: true
                        }
                    }
                }
            });
            <?php endif; ?>
        });
    </script>
    <script src="js/global.js"></script>

    <?php include 'footer.php'; ?>
</body>
</html>